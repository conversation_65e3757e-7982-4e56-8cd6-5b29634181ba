/**
 * تصميم مشغل الفيديو المتقدم
 * Advanced Video Player Styles
 */

.advanced-video-player {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #000;
}

.youtube-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* طبقة التراكب */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* زر التشغيل الكبير */
.play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    pointer-events: auto;
    transition: opacity 0.3s ease;
}

.play-btn-large {
    width: 80px;
    height: 80px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn-large:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* مؤشر التحميل */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    pointer-events: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* عناصر التحكم */
.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 15px 15px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 20;
}

.video-controls.visible {
    opacity: 1;
    visibility: visible;
}

.controls-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* شريط التقدم */
.progress-container {
    position: relative;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    margin-bottom: 5px;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    overflow: hidden;
}

.progress-buffer {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-played {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #ff4444;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 14px;
    height: 14px;
    background: #ff4444;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    left: 0%;
}

.progress-container:hover .progress-handle {
    opacity: 1;
}

/* أزرار التحكم */
.controls-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.controls-left,
.controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 36px;
    height: 36px;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.control-btn.play-pause-btn {
    font-size: 18px;
}

.btn-text {
    font-size: 10px;
    font-weight: bold;
}

/* التحكم في الصوت */
.volume-control {
    position: relative;
    display: flex;
    align-items: center;
}

.volume-slider {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.volume-control:hover .volume-slider {
    opacity: 1;
    visibility: visible;
}

.volume-slider input[type="range"] {
    writing-mode: bt-lr; /* IE */
    -webkit-appearance: slider-vertical; /* WebKit */
    width: 30px;
    height: 80px;
    background: transparent;
    outline: none;
}

/* عرض الوقت */
.time-display {
    color: white;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
}

.time-separator {
    opacity: 0.7;
}

/* قائمة الإعدادات */
.settings-menu {
    position: absolute;
    bottom: 60px;
    right: 15px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    padding: 15px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 30;
}

.settings-menu.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    color: white;
    font-size: 14px;
}

.settings-item:last-child {
    margin-bottom: 0;
}

.settings-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.settings-item select option {
    background: #333;
    color: white;
}

/* رسائل الخطأ */
.video-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 40;
}

.error-content {
    text-align: center;
    color: white;
    padding: 20px;
}

.error-content i {
    font-size: 48px;
    color: #ff4444;
    margin-bottom: 15px;
}

.error-content p {
    font-size: 16px;
    margin: 0;
}

/* الشاشة الكاملة */
.advanced-video-player:-webkit-full-screen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:-moz-full-screen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:fullscreen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:fullscreen .video-wrapper {
    padding-bottom: 0;
    height: 100vh;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .controls-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .controls-left,
    .controls-right {
        justify-content: center;
        width: 100%;
    }
    
    .play-btn-large {
        width: 60px;
        height: 60px;
        font-size: 20px;
    }
    
    .control-btn {
        font-size: 14px;
        padding: 6px;
        min-width: 32px;
        height: 32px;
    }
    
    .time-display {
        font-size: 12px;
        min-width: 70px;
    }
    
    .volume-slider {
        position: static;
        transform: none;
        margin: 0;
        opacity: 1;
        visibility: visible;
        background: transparent;
        padding: 0;
    }
    
    .volume-slider input[type="range"] {
        writing-mode: initial;
        -webkit-appearance: none;
        width: 80px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }
    
    .volume-slider input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 14px;
        height: 14px;
        background: #ff4444;
        border-radius: 50%;
        cursor: pointer;
    }
    
    .settings-menu {
        right: 50%;
        transform: translateX(50%) translateY(10px);
        min-width: 180px;
    }
    
    .settings-menu.visible {
        transform: translateX(50%) translateY(0);
    }
}

@media (max-width: 480px) {
    .video-controls {
        padding: 15px 10px 10px;
    }
    
    .controls-left,
    .controls-right {
        gap: 8px;
    }
    
    .control-btn {
        font-size: 12px;
        padding: 4px;
        min-width: 28px;
        height: 28px;
    }
    
    .play-btn-large {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }
}
